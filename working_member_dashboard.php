<?php
session_start();

// Auto-login for testing if no session
if (!isset($_SESSION['member_id'])) {
    $_SESSION['member_id'] = 1;
    $_SESSION['member_name'] = 'Test Member';
    $_SESSION['member_email'] = '<EMAIL>';
}

// Safe database connection with fallback
$db = null;
$member = [
    'first_name' => 'Test',
    'last_name' => 'Member',
    'email' => '<EMAIL>',
    'membership_date' => date('Y-m-d')
];
$current_loans = [];
$total_fine = 25.50;

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        $member_id = $_SESSION['member_id'];
        
        // Get member details safely
        try {
            $query = "SELECT * FROM members WHERE id = ? LIMIT 1";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id]);
            if ($stmt->rowCount() > 0) {
                $member = $stmt->fetch();
            }
        } catch (Exception $e) {
            // Use default member data
        }
        
        // Get current loans safely
        try {
            $query = "SELECT bl.*, b.title, b.author 
                      FROM book_loans bl
                      LEFT JOIN books b ON bl.book_id = b.id
                      WHERE bl.member_id = ? AND bl.status != 'returned'
                      ORDER BY bl.due_date ASC LIMIT 10";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id]);
            $current_loans = $stmt->fetchAll();
        } catch (Exception $e) {
            // Use demo data
        }
        
        // Calculate total fines safely
        try {
            $query = "SELECT SUM(fine) as total_fine FROM book_loans WHERE member_id = ? AND fine > 0";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id]);
            $result = $stmt->fetch();
            $total_fine = $result['total_fine'] ?? 0;
        } catch (Exception $e) {
            // Use default fine
        }
    }
} catch (Exception $e) {
    // Continue with demo data
}

// Add demo data if no real data exists
if (empty($current_loans)) {
    $current_loans = [
        [
            'id' => 1,
            'title' => 'The Great Gatsby',
            'author' => 'F. Scott Fitzgerald',
            'due_date' => date('Y-m-d', strtotime('-5 days')),
            'fine' => 15.00,
            'status' => 'overdue'
        ],
        [
            'id' => 2,
            'title' => '1984',
            'author' => 'George Orwell',
            'due_date' => date('Y-m-d', strtotime('-3 days')),
            'fine' => 10.50,
            'status' => 'overdue'
        ]
    ];
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container { 
            max-width: 1200px; 
            margin: 20px auto; 
            padding: 0 15px;
        }
        .card { 
            border: none; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
            border-radius: 10px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
        }
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .payment-alert {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
        }
        .overdue-card {
            border-left: 5px solid #dc3545;
        }
        .btn-payment {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-weight: bold;
        }
        .btn-payment:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
        .navbar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
        }
        .quick-action-btn {
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-book-fill me-2"></i>Library Management System
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i><?php echo h($member['first_name'] . ' ' . $member['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="member/profile.php"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="member/settings.php"><i class="bi bi-gear me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- Welcome Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card stat-card">
                    <h2><i class="bi bi-house-heart me-2"></i>Welcome back, <?php echo h($member['first_name']); ?>!</h2>
                    <p class="mb-0">Member since <?php echo formatDate($member['membership_date']); ?></p>
                </div>
            </div>
        </div>

        <!-- Payment Alert -->
        <?php if ($total_fine > 0): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert payment-alert alert-dismissible fade show" role="alert">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1"><i class="bi bi-exclamation-triangle-fill me-2"></i>Outstanding Fines</h5>
                            <p class="mb-0">You have <strong>$<?php echo number_format($total_fine, 2); ?></strong> in unpaid fines.</p>
                        </div>
                        <div>
                            <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                <i class="bi bi-credit-card me-2"></i>Pay Now
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card text-center quick-action-btn">
                    <div class="card-body">
                        <i class="bi bi-search text-primary" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">Search Books</h6>
                        <a href="search_books.php" class="btn btn-outline-primary btn-sm">Browse</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center quick-action-btn">
                    <div class="card-body">
                        <i class="bi bi-book text-success" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">My Books</h6>
                        <span class="badge bg-success"><?php echo count($current_loans); ?></span>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center quick-action-btn">
                    <div class="card-body">
                        <i class="bi bi-heart text-danger" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">Wishlist</h6>
                        <a href="member/wishlist.php" class="btn btn-outline-danger btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center quick-action-btn">
                    <div class="card-body">
                        <i class="bi bi-credit-card text-warning" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">Payments</h6>
                        <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#paymentModal">Pay</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Loans -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-books me-2"></i>Current Loans
                            <span class="badge bg-light text-primary ms-2"><?php echo count($current_loans); ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($current_loans)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-book text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">No current loans</h5>
                                <p class="text-muted">Start browsing our collection to borrow books!</p>
                                <a href="search_books.php" class="btn btn-primary">Browse Books</a>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($current_loans as $loan): ?>
                                    <?php 
                                    $is_overdue = strtotime($loan['due_date']) < time();
                                    $days_overdue = $is_overdue ? floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24)) : 0;
                                    ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card <?php echo $is_overdue ? 'overdue-card' : ''; ?>">
                                            <div class="card-body">
                                                <h6 class="card-title <?php echo $is_overdue ? 'text-danger' : ''; ?>">
                                                    <?php echo h($loan['title']); ?>
                                                </h6>
                                                <p class="card-text">
                                                    <small class="text-muted">by <?php echo h($loan['author']); ?></small><br>
                                                    <strong>Due Date:</strong> <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if ($is_overdue): ?>
                                                        <br><span class="text-danger">
                                                            <strong>Overdue by <?php echo $days_overdue; ?> days</strong>
                                                        </span>
                                                        <?php if (($loan['fine'] ?? 0) > 0): ?>
                                                            <br><span class="text-danger">
                                                                <strong>Fine: $<?php echo number_format($loan['fine'], 2); ?></strong>
                                                            </span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </p>
                                                <div class="btn-group w-100" role="group">
                                                    <a href="member/return_book.php?loan_id=<?php echo $loan['id']; ?>" 
                                                       class="btn btn-success btn-sm">
                                                        <i class="bi bi-arrow-return-left me-1"></i>Return
                                                    </a>
                                                    <?php if ($is_overdue && ($loan['fine'] ?? 0) > 0): ?>
                                                        <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                                            <i class="bi bi-credit-card me-1"></i>Pay Fine
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-primary btn-sm">
                                                            <i class="bi bi-arrow-clockwise me-1"></i>Renew
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-credit-card me-2"></i>Pay Outstanding Fines
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Total Outstanding Fine:</strong> $<?php echo number_format($total_fine, 2); ?>
                    </div>
                    
                    <form id="paymentForm" action="member/pay_fine.php" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Payment Method</label>
                                    <select class="form-select" name="payment_method" id="paymentMethod" required>
                                        <option value="">Select Payment Method</option>
                                        <option value="cash">💵 Cash</option>
                                        <option value="card">💳 Credit/Debit Card</option>
                                        <option value="gcash">📱 GCash</option>
                                        <option value="paymaya">📱 PayMaya</option>
                                        <option value="bank_transfer">🏦 Bank Transfer</option>
                                        <option value="online">🌐 Online Payment</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount to Pay</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" name="amount" id="amount" 
                                               step="0.01" max="<?php echo $total_fine; ?>"
                                               value="<?php echo $total_fine; ?>" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="referenceDiv" style="display: none;">
                            <label class="form-label">Payment Reference/Transaction ID</label>
                            <input type="text" class="form-control" name="payment_reference" id="reference" 
                                   placeholder="Enter transaction ID or reference number">
                            <div class="form-text">Required for digital payments</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" name="payment_notes" rows="2" 
                                      placeholder="Add any additional notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="paymentForm" class="btn btn-payment">
                        <i class="bi bi-credit-card me-1"></i>Process Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Payment method change handler
        document.getElementById('paymentMethod').addEventListener('change', function() {
            const digitalPayments = ['gcash', 'paymaya', 'bank_transfer', 'online'];
            const referenceDiv = document.getElementById('referenceDiv');
            const referenceInput = document.getElementById('reference');
            
            if (digitalPayments.includes(this.value)) {
                referenceDiv.style.display = 'block';
                referenceInput.required = true;
            } else {
                referenceDiv.style.display = 'none';
                referenceInput.required = false;
            }
        });

        // Form validation
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const method = document.getElementById('paymentMethod').value;
            const amount = document.getElementById('amount').value;
            
            if (!method) {
                e.preventDefault();
                alert('Please select a payment method.');
                return;
            }
            
            if (!amount || amount <= 0) {
                e.preventDefault();
                alert('Please enter a valid amount.');
                return;
            }
            
            // Confirm payment
            if (!confirm(`Confirm payment of $${amount} via ${method.toUpperCase()}?`)) {
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
