<?php
/**
 * Generate Sample Payment Data for Payment Reports
 * This script creates realistic payment transactions for testing the payment reports system
 */

session_start();
require_once 'config/database.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Generate Payment Data - LMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; color: #000; }
    </style>
</head>
<body>
<div class='container mt-4'>
    <h1 class='mb-4'>💳 Generate Sample Payment Data</h1>";

try {
    // First, ensure all required tables exist
    echo "<h3>🔧 Setting up required tables...</h3>";

    // Create users table if not exists (for processed_by reference)
    $create_users = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `email` varchar(100) NOT NULL UNIQUE,
        `role` enum('admin','librarian','staff') DEFAULT 'staff',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    )";
    $db->exec($create_users);
    echo "<p class='success'>✅ Users table verified</p>";

    // Create members table if not exists
    $create_members = "CREATE TABLE IF NOT EXISTS `members` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `first_name` varchar(100) NOT NULL,
        `last_name` varchar(100) NOT NULL,
        `email` varchar(255) NOT NULL UNIQUE,
        `password` varchar(255),
        `phone` varchar(20),
        `address` text,
        `membership_date` date DEFAULT (CURDATE()),
        `membership_status` enum('active','inactive','suspended') DEFAULT 'active',
        `google_id` varchar(255),
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    )";
    $db->exec($create_members);
    echo "<p class='success'>✅ Members table verified</p>";

    // Create books table if not exists
    $create_books = "CREATE TABLE IF NOT EXISTS `books` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `author` varchar(255) NOT NULL,
        `isbn` varchar(20) UNIQUE,
        `category` varchar(100),
        `description` text,
        `publication_year` int(11),
        `total_quantity` int(11) DEFAULT 1,
        `available_quantity` int(11) DEFAULT 1,
        `cover_image` varchar(255),
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    )";
    $db->exec($create_books);
    echo "<p class='success'>✅ Books table verified</p>";

    // Create book_loans table if not exists
    $create_loans = "CREATE TABLE IF NOT EXISTS `book_loans` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `book_id` int(11) NOT NULL,
        `issue_date` date NOT NULL,
        `due_date` date NOT NULL,
        `return_date` date DEFAULT NULL,
        `status` enum('borrowed','returned','overdue') DEFAULT 'borrowed',
        `fine` decimal(10,2) DEFAULT 0.00,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_book_id` (`book_id`)
    )";
    $db->exec($create_loans);
    echo "<p class='success'>✅ Book loans table verified</p>";

    // Create fines table if not exists
    $create_fines = "CREATE TABLE IF NOT EXISTS `fines` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `member_id` int(11) NOT NULL,
        `loan_id` int(11) DEFAULT NULL,
        `amount` decimal(10,2) NOT NULL,
        `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
        `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL,
        `payment_reference` varchar(100) DEFAULT NULL,
        `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `paid_date` timestamp NULL DEFAULT NULL,
        `waived_date` timestamp NULL DEFAULT NULL,
        `processed_by` int(11) DEFAULT NULL,
        `notes` text DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_loan_id` (`loan_id`),
        KEY `idx_status` (`status`)
    )";
    $db->exec($create_fines);
    echo "<p class='success'>✅ Fines table verified</p>";

    // Create payment_transactions table if not exists
    $create_transactions = "CREATE TABLE IF NOT EXISTS `payment_transactions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `fine_id` int(11) NOT NULL,
        `member_id` int(11) NOT NULL,
        `amount` decimal(10,2) NOT NULL,
        `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') NOT NULL,
        `payment_reference` varchar(100) DEFAULT NULL,
        `transaction_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `processed_by` int(11) NOT NULL,
        `receipt_number` varchar(50) UNIQUE NOT NULL,
        `status` enum('pending','completed','failed','refunded') DEFAULT 'completed',
        `notes` text DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `idx_fine_id` (`fine_id`),
        KEY `idx_member_id` (`member_id`),
        KEY `idx_payment_method` (`payment_method`),
        KEY `idx_transaction_date` (`transaction_date`),
        KEY `idx_status` (`status`)
    )";
    $db->exec($create_transactions);
    echo "<p class='success'>✅ Payment transactions table verified</p>";

    echo "<h3>📊 Generating sample data...</h3>";

    // Insert admin user if not exists
    $check_admin = $db->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'")->fetch();
    if ($check_admin['count'] == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', $admin_password, '<EMAIL>', 'admin']);
        echo "<p class='success'>✅ Admin user created</p>";
    }

    // Insert librarian user if not exists
    $check_librarian = $db->query("SELECT COUNT(*) as count FROM users WHERE username = 'librarian'")->fetch();
    if ($check_librarian['count'] == 0) {
        $librarian_password = password_hash('librarian123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['librarian', $librarian_password, '<EMAIL>', 'librarian']);
        echo "<p class='success'>✅ Librarian user created</p>";
    }

    // Sample member data
    $sample_members = [
        ['Maria', 'Santos', '<EMAIL>', '09171234567', '123 Rizal Street, Manila'],
        ['Juan', 'Dela Cruz', '<EMAIL>', '09281234567', '456 Bonifacio Ave, Quezon City'],
        ['Ana', 'Reyes', '<EMAIL>', '09391234567', '789 Mabini Street, Makati'],
        ['Carlos', 'Garcia', '<EMAIL>', '09401234567', '321 Luna Street, Pasig'],
        ['Sofia', 'Mendoza', '<EMAIL>', '09511234567', '654 Aguinaldo Ave, Cavite'],
        ['Miguel', 'Torres', '<EMAIL>', '09621234567', '987 Lapu-Lapu Street, Cebu'],
        ['Isabella', 'Flores', '<EMAIL>', '09731234567', '147 Magellan Road, Davao'],
        ['Diego', 'Morales', '<EMAIL>', '09841234567', '258 Legaspi Street, Iloilo'],
        ['Carmen', 'Ramos', '<EMAIL>', '09951234567', '369 Roxas Boulevard, Manila'],
        ['Roberto', 'Cruz', '<EMAIL>', '09061234567', '741 EDSA, Mandaluyong']
    ];

    // Insert members if they don't exist
    foreach ($sample_members as $member) {
        $check_member = $db->prepare("SELECT COUNT(*) as count FROM members WHERE email = ?");
        $check_member->execute([$member[2]]);
        if ($check_member->fetch()['count'] == 0) {
            $stmt = $db->prepare("INSERT INTO members (first_name, last_name, email, phone, address, membership_date) VALUES (?, ?, ?, ?, ?, ?)");
            $membership_date = date('Y-m-d', strtotime('-' . rand(30, 365) . ' days'));
            $stmt->execute([$member[0], $member[1], $member[2], $member[3], $member[4], $membership_date]);
        }
    }
    echo "<p class='success'>✅ Sample members created</p>";

    // Sample book data
    $sample_books = [
        ['Noli Me Tangere', 'Jose Rizal', '978-***********-1', 'Classic Literature'],
        ['El Filibusterismo', 'Jose Rizal', '978-***********-2', 'Classic Literature'],
        ['Florante at Laura', 'Francisco Balagtas', '978-***********-3', 'Poetry'],
        ['Mga Ibong Mandaragit', 'Amado V. Hernandez', '978-***********-4', 'Social Commentary'],
        ['Banaag at Sikat', 'Lope K. Santos', '978-***********-5', 'Social Realism'],
        ['Dekada 70', 'Lualhati Bautista', '978-***********-6', 'Contemporary Fiction'],
        ['Smaller and Smaller Circles', 'F.H. Batacan', '978-***********-7', 'Crime Fiction'],
        ['The Woman Who Had Two Navels', 'Nick Joaquin', '978-***********-8', 'Magical Realism'],
        ['Po-on', 'F. Sionil Jose', '978-***********-9', 'Historical Fiction'],
        ['State of War', 'Ninotchka Rosca', '978-***********-0', 'Political Fiction']
    ];

    // Insert books if they don't exist
    foreach ($sample_books as $book) {
        $check_book = $db->prepare("SELECT COUNT(*) as count FROM books WHERE isbn = ?");
        $check_book->execute([$book[2]]);
        if ($check_book->fetch()['count'] == 0) {
            $stmt = $db->prepare("INSERT INTO books (title, author, isbn, category, total_quantity, available_quantity) VALUES (?, ?, ?, ?, ?, ?)");
            $quantity = rand(3, 10);
            $stmt->execute([$book[0], $book[1], $book[2], $book[3], $quantity, $quantity - rand(0, 2)]);
        }
    }
    echo "<p class='success'>✅ Sample books created</p>";

    // Get member and book IDs for creating loans and fines
    $members = $db->query("SELECT id FROM members ORDER BY id")->fetchAll();
    $books = $db->query("SELECT id FROM books ORDER BY id")->fetchAll();
    $admin_id = $db->query("SELECT id FROM users WHERE username = 'admin'")->fetch()['id'];
    $librarian_id = $db->query("SELECT id FROM users WHERE username = 'librarian'")->fetch()['id'];

    if (empty($members) || empty($books)) {
        throw new Exception("No members or books found to create loans");
    }

    echo "<h3>📚 Creating book loans with fines...</h3>";

    // Create book loans with some overdue (to generate fines)
    $loan_count = 0;
    for ($i = 0; $i < 25; $i++) {
        $member_id = $members[array_rand($members)]['id'];
        $book_id = $books[array_rand($books)]['id'];

        // Random issue date in the past 3 months
        $issue_date = date('Y-m-d', strtotime('-' . rand(10, 90) . ' days'));
        $due_date = date('Y-m-d', strtotime($issue_date . ' +14 days'));

        // Some loans are returned, some are overdue
        $is_returned = rand(0, 100) < 60; // 60% chance of being returned
        $return_date = null;
        $status = 'borrowed';
        $fine = 0;

        if ($is_returned) {
            $return_date = date('Y-m-d', strtotime($due_date . ' +' . rand(-5, 10) . ' days'));
            $status = 'returned';

            // If returned late, calculate fine
            if (strtotime($return_date) > strtotime($due_date)) {
                $days_late = (strtotime($return_date) - strtotime($due_date)) / (60 * 60 * 24);
                $fine = $days_late * 5; // ₱5 per day fine
            }
        } else {
            // Check if overdue
            if (strtotime($due_date) < time()) {
                $status = 'overdue';
                $days_late = (time() - strtotime($due_date)) / (60 * 60 * 24);
                $fine = $days_late * 5; // ₱5 per day fine
            }
        }

        // Check if this loan combination already exists
        $check_loan = $db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE member_id = ? AND book_id = ? AND issue_date = ?");
        $check_loan->execute([$member_id, $book_id, $issue_date]);

        if ($check_loan->fetch()['count'] == 0) {
            $stmt = $db->prepare("INSERT INTO book_loans (member_id, book_id, issue_date, due_date, return_date, status, fine) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$member_id, $book_id, $issue_date, $due_date, $return_date, $status, $fine]);
            $loan_count++;
        }
    }
    echo "<p class='success'>✅ Created $loan_count book loans</p>";

    echo "<h3>💰 Creating fines and payment transactions...</h3>";

    // Get loans with fines
    $loans_with_fines = $db->query("SELECT * FROM book_loans WHERE fine > 0")->fetchAll();

    $fine_count = 0;
    $payment_count = 0;

    foreach ($loans_with_fines as $loan) {
        // Create fine record
        $stmt = $db->prepare("INSERT INTO fines (member_id, loan_id, amount, status, created_date) VALUES (?, ?, ?, ?, ?)");
        $fine_status = rand(0, 100) < 70 ? 'paid' : 'unpaid'; // 70% chance of being paid
        $fine_created_date = date('Y-m-d H:i:s', strtotime($loan['return_date'] ?? $loan['due_date'] . ' +1 day'));
        $stmt->execute([$loan['member_id'], $loan['id'], $loan['fine'], $fine_status, $fine_created_date]);
        $fine_id = $db->lastInsertId();
        $fine_count++;

        // If fine is paid, create payment transaction
        if ($fine_status === 'paid') {
            $payment_methods = ['cash', 'gcash', 'paymaya', 'bank_transfer', 'credit_card', 'debit_card'];
            $payment_method = $payment_methods[array_rand($payment_methods)];

            // Generate payment reference for digital payments
            $payment_reference = null;
            if (in_array($payment_method, ['gcash', 'paymaya', 'bank_transfer', 'credit_card', 'debit_card'])) {
                $payment_reference = strtoupper($payment_method) . '-' . date('Ymd') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
            }

            // Generate receipt number
            $receipt_number = 'RCP-' . date('Ymd', strtotime($fine_created_date)) . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

            // Random payment date (within 1-30 days after fine creation)
            $payment_date = date('Y-m-d H:i:s', strtotime($fine_created_date . ' +' . rand(1, 30) . ' days'));

            // Random processor (admin or librarian)
            $processed_by = rand(0, 1) ? $admin_id : $librarian_id;

            // Payment amount (could be partial or full)
            $payment_amount = rand(0, 100) < 80 ? $loan['fine'] : round($loan['fine'] * rand(50, 99) / 100, 2);

            $stmt = $db->prepare("INSERT INTO payment_transactions (fine_id, member_id, amount, payment_method, payment_reference, transaction_date, processed_by, receipt_number, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $notes = "Payment for overdue book fine - " . ($payment_amount == $loan['fine'] ? 'Full payment' : 'Partial payment');
            $stmt->execute([$fine_id, $loan['member_id'], $payment_amount, $payment_method, $payment_reference, $payment_date, $processed_by, $receipt_number, 'completed', $notes]);

            // Update fine record with payment details
            $update_fine = $db->prepare("UPDATE fines SET payment_method = ?, payment_reference = ?, paid_date = ?, processed_by = ?, notes = ? WHERE id = ?");
            $update_fine->execute([$payment_method, $payment_reference, $payment_date, $processed_by, $notes, $fine_id]);

            $payment_count++;
        }
    }

    echo "<p class='success'>✅ Created $fine_count fines</p>";
    echo "<p class='success'>✅ Created $payment_count payment transactions</p>";

    // Generate summary statistics
    echo "<h3>📈 Summary Statistics</h3>";

    $stats = $db->query("SELECT
        COUNT(DISTINCT pt.member_id) as paying_members,
        COUNT(pt.id) as total_transactions,
        SUM(pt.amount) as total_collected,
        AVG(pt.amount) as avg_payment,
        COUNT(CASE WHEN pt.payment_method = 'cash' THEN 1 END) as cash_payments,
        COUNT(CASE WHEN pt.payment_method = 'gcash' THEN 1 END) as gcash_payments,
        COUNT(CASE WHEN pt.payment_method = 'paymaya' THEN 1 END) as paymaya_payments,
        COUNT(CASE WHEN pt.payment_method = 'bank_transfer' THEN 1 END) as bank_payments,
        COUNT(CASE WHEN pt.payment_method = 'credit_card' THEN 1 END) as credit_payments,
        COUNT(CASE WHEN pt.payment_method = 'debit_card' THEN 1 END) as debit_payments
        FROM payment_transactions pt
        WHERE pt.status = 'completed'")->fetch();

    echo "<div class='row mt-4'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Payment Statistics</h5>";
    echo "<ul class='list-group list-group-flush'>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Paying Members:</span><strong>" . ($stats['paying_members'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Total Transactions:</span><strong>" . ($stats['total_transactions'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Total Collected:</span><strong>₱" . number_format($stats['total_collected'] ?? 0, 2) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Average Payment:</span><strong>₱" . number_format($stats['avg_payment'] ?? 0, 2) . "</strong></li>";
    echo "</ul>";
    echo "</div></div></div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Payment Methods</h5>";
    echo "<ul class='list-group list-group-flush'>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Cash:</span><strong>" . ($stats['cash_payments'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>GCash:</span><strong>" . ($stats['gcash_payments'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>PayMaya:</span><strong>" . ($stats['paymaya_payments'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Bank Transfer:</span><strong>" . ($stats['bank_payments'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Credit Card:</span><strong>" . ($stats['credit_payments'] ?? 0) . "</strong></li>";
    echo "<li class='list-group-item d-flex justify-content-between'><span>Debit Card:</span><strong>" . ($stats['debit_payments'] ?? 0) . "</strong></li>";
    echo "</ul>";
    echo "</div></div></div>";
    echo "</div>";

    echo "<div class='alert alert-success mt-4' role='alert'>";
    echo "<h4 class='alert-heading'>🎉 Success!</h4>";
    echo "<p>Sample payment data has been generated successfully! You can now:</p>";
    echo "<ul>";
    echo "<li><a href='admin/payment_reports.php' class='alert-link'>View Payment Reports</a></li>";
    echo "<li><a href='admin/payment_processing.php' class='alert-link'>Process New Payments</a></li>";
    echo "<li><a href='admin/financial_management.php' class='alert-link'>Manage Financial Data</a></li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger' role='alert'>";
    echo "<h4 class='alert-heading'>❌ Error!</h4>";
    echo "<p>An error occurred while generating payment data:</p>";
    echo "<p><strong>" . htmlspecialchars($e->getMessage()) . "</strong></p>";
    echo "</div>";
}

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
