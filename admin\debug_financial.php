<?php
/**
 * Debug Financial Management Issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

echo "<h1>Financial Management Debug</h1>";

// Test database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check if required tables exist
$required_tables = ['fines', 'payment_transactions', 'members', 'users', 'books', 'book_loans'];

echo "<h2>Table Existence Check</h2>";
foreach ($required_tables as $table) {
    try {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
            
            // Check table structure for important tables
            if ($table === 'fines') {
                $query = "DESCRIBE $table";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $columns = $stmt->fetchAll();
                echo "<ul>";
                foreach ($columns as $column) {
                    echo "<li>{$column['Field']} - {$column['Type']}</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Table '$table' does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking table '$table': " . $e->getMessage() . "</p>";
    }
}

// Test authentication functions
echo "<h2>Authentication Functions Test</h2>";
$auth_functions = ['isLoggedIn', 'isAdmin', 'isLibrarian', 'isStaffWithFinancialAccess'];
foreach ($auth_functions as $function) {
    if (function_exists($function)) {
        echo "<p style='color: green;'>✅ Function '$function' exists</p>";
        try {
            $result = $function();
            echo "<p>Result: " . ($result ? 'true' : 'false') . "</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error calling $function: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Function '$function' missing</p>";
    }
}

// Test session data
echo "<h2>Session Data</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Test basic query
echo "<h2>Basic Query Test</h2>";
try {
    $query = "SELECT COUNT(*) as count FROM members";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ Members count: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
}

// Create missing tables if needed
echo "<h2>Create Missing Tables</h2>";
if (isset($_GET['create_tables'])) {
    try {
        // Create fines table
        $create_fines = "CREATE TABLE IF NOT EXISTS `fines` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `member_id` int(11) NOT NULL,
            `loan_id` int(11) DEFAULT NULL,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `paid_date` timestamp NULL DEFAULT NULL,
            `waived_date` timestamp NULL DEFAULT NULL,
            `processed_by` int(11) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_loan_id` (`loan_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_date` (`created_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $db->exec($create_fines);
        echo "<p style='color: green;'>✅ Fines table created</p>";
        
        // Create payment_transactions table
        $create_transactions = "CREATE TABLE IF NOT EXISTS `payment_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `fine_id` int(11) NOT NULL,
            `member_id` int(11) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') NOT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `transaction_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `processed_by` int(11) NOT NULL,
            `receipt_number` varchar(50) UNIQUE NOT NULL,
            `status` enum('pending','completed','failed','refunded') DEFAULT 'completed',
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_fine_id` (`fine_id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_payment_method` (`payment_method`),
            KEY `idx_transaction_date` (`transaction_date`),
            KEY `idx_receipt_number` (`receipt_number`),
            KEY `idx_processed_by` (`processed_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $db->exec($create_transactions);
        echo "<p style='color: green;'>✅ Payment transactions table created</p>";
        
        // Insert sample data
        $sample_fines = "INSERT IGNORE INTO fines (member_id, amount, status) VALUES 
            (1, 5.00, 'unpaid'),
            (1, 2.50, 'paid'),
            (2, 7.50, 'unpaid')";
        $db->exec($sample_fines);
        echo "<p style='color: green;'>✅ Sample fines data inserted</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating tables: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p><a href='?create_tables=1' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Create Missing Tables</a></p>";
}

echo "<h2>Test Financial Management Page</h2>";
echo "<p><a href='financial_management.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Go to Financial Management</a></p>";
?>
