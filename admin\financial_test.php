<?php
/**
 * Simple Financial Management Test Page
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

echo "<h1>Financial Management Test</h1>";

// Connect to database and auto-login as admin for testing
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
        // Force admin login for testing
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
            echo "<p style='color: green;'>✅ Auto-logged in as admin: " . $admin['username'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ No admin user found</p>";
            exit;
        }
    } else {
        echo "<p style='color: green;'>✅ Already logged in</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    exit;
}

// Check if fines table exists
try {
    $check_table = $db->query("SHOW TABLES LIKE 'fines'");
    if (!$check_table->fetch()) {
        echo "<p style='color: orange;'>⚠️ Fines table doesn't exist, creating it...</p>";
        
        // Create fines table if it doesn't exist
        $create_fines = "CREATE TABLE IF NOT EXISTS `fines` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `member_id` int(11) NOT NULL,
            `loan_id` int(11) DEFAULT NULL,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `paid_date` timestamp NULL DEFAULT NULL,
            `waived_date` timestamp NULL DEFAULT NULL,
            `processed_by` int(11) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_loan_id` (`loan_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_date` (`created_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $db->exec($create_fines);
        echo "<p style='color: green;'>✅ Fines table created</p>";
        
        // Insert sample data
        $sample_fines = "INSERT IGNORE INTO fines (member_id, amount, status) VALUES 
            (1, 5.00, 'unpaid'),
            (1, 2.50, 'paid'),
            (2, 7.50, 'unpaid')";
        $db->exec($sample_fines);
        echo "<p style='color: green;'>✅ Sample data inserted</p>";
    } else {
        echo "<p style='color: green;'>✅ Fines table exists</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Table setup error: " . $e->getMessage() . "</p>";
}

// Test basic query
try {
    $query = "SELECT COUNT(*) as count FROM fines";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ Fines count: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Query error: " . $e->getMessage() . "</p>";
}

echo "<h2>Test Links</h2>";
echo "<p><a href='financial_management.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Go to Financial Management</a></p>";
echo "<p><a href='debug_financial.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Debug Financial</a></p>";
echo "<p><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Admin Dashboard</a></p>";

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header">
                <h3><i class="bi bi-currency-dollar me-2"></i>Simple Financial Management</h3>
            </div>
            <div class="card-body">
                <?php
                // Get financial statistics
                try {
                    $query = "SELECT
                                COUNT(*) as total_fines,
                                COALESCE(SUM(amount), 0) as total_amount,
                                COALESCE(SUM(CASE WHEN status = 'unpaid' THEN amount ELSE 0 END), 0) as unpaid_amount,
                                COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
                                COALESCE(SUM(CASE WHEN status = 'waived' THEN amount ELSE 0 END), 0) as waived_amount
                              FROM fines";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    $stats = $stmt->fetch();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'><div class='card bg-primary text-white'><div class='card-body'><h5>Total Fines</h5><h3>$" . number_format($stats['total_amount'], 2) . "</h3></div></div></div>";
                    echo "<div class='col-md-3'><div class='card bg-danger text-white'><div class='card-body'><h5>Unpaid</h5><h3>$" . number_format($stats['unpaid_amount'], 2) . "</h3></div></div></div>";
                    echo "<div class='col-md-3'><div class='card bg-success text-white'><div class='card-body'><h5>Paid</h5><h3>$" . number_format($stats['paid_amount'], 2) . "</h3></div></div></div>";
                    echo "<div class='col-md-3'><div class='card bg-secondary text-white'><div class='card-body'><h5>Waived</h5><h3>$" . number_format($stats['waived_amount'], 2) . "</h3></div></div></div>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Stats error: " . $e->getMessage() . "</p>";
                }
                ?>
                
                <h4 class="mt-4">Recent Fines</h4>
                <?php
                try {
                    // Check if members table has the right column names
                    $member_columns = $db->query("SHOW COLUMNS FROM members LIKE 'name'")->fetch();
                    $name_column = $member_columns ? 'name' : 'CONCAT(COALESCE(first_name, ""), " ", COALESCE(last_name, ""))';
                    
                    $query = "SELECT f.*, $name_column as member_name, m.email as member_email
                              FROM fines f
                              JOIN members m ON f.member_id = m.id
                              ORDER BY f.created_date DESC
                              LIMIT 10";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    $fines = $stmt->fetchAll();
                    
                    if (empty($fines)) {
                        echo "<p>No fines found.</p>";
                    } else {
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>Member</th><th>Amount</th><th>Status</th><th>Date</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($fines as $fine) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($fine['member_name']) . "</td>";
                            echo "<td>$" . number_format($fine['amount'], 2) . "</td>";
                            echo "<td><span class='badge bg-" . ($fine['status'] == 'paid' ? 'success' : ($fine['status'] == 'unpaid' ? 'danger' : 'secondary')) . "'>" . ucfirst($fine['status']) . "</span></td>";
                            echo "<td>" . date('M j, Y', strtotime($fine['created_date'])) . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Fines list error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>
