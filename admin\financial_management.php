<?php
/**
 * Financial Management - Library Fines and Payments
 * Track overdue fines, payments, and financial reports
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Connect to database and auto-login as admin for testing (remove in production)
try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception("Database connection failed");
    }

    if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
        // Force admin login for testing
        $query = "SELECT * FROM users WHERE role = 'admin' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $admin = $stmt->fetch();

        if ($admin) {
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['username'] = $admin['username'];
            $_SESSION['role'] = $admin['role'];
            $_SESSION['logged_in'] = true;
        } else {
            redirect('../login.php');
            exit;
        }
    }
} catch (Exception $e) {
    die("Database Error: " . $e->getMessage());
}

// Check if fines table exists
try {
    $check_table = $db->query("SHOW TABLES LIKE 'fines'");
    if (!$check_table->fetch()) {
        // Create fines table if it doesn't exist
        $create_fines = "CREATE TABLE IF NOT EXISTS `fines` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `member_id` int(11) NOT NULL,
            `loan_id` int(11) DEFAULT NULL,
            `amount` decimal(10,2) NOT NULL,
            `status` enum('unpaid','paid','waived') DEFAULT 'unpaid',
            `payment_method` enum('cash','gcash','paymaya','bank_transfer','credit_card','debit_card') DEFAULT NULL,
            `payment_reference` varchar(100) DEFAULT NULL,
            `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `paid_date` timestamp NULL DEFAULT NULL,
            `waived_date` timestamp NULL DEFAULT NULL,
            `processed_by` int(11) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_member_id` (`member_id`),
            KEY `idx_loan_id` (`loan_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_date` (`created_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        $db->exec($create_fines);

        // Insert sample data
        $sample_fines = "INSERT IGNORE INTO fines (member_id, amount, status) VALUES
            (1, 5.00, 'unpaid'),
            (1, 2.50, 'paid'),
            (2, 7.50, 'unpaid')";
        $db->exec($sample_fines);
    }
} catch (Exception $e) {
    die("Table setup error: " . $e->getMessage());
}

// Handle fine actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['mark_paid'])) {
            $fine_id = (int)$_POST['fine_id'];
            $query = "UPDATE fines SET status = 'paid', paid_date = NOW() WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $fine_id);
            $stmt->execute();

            $success_message = "Fine marked as paid successfully.";
        } elseif (isset($_POST['waive_fine'])) {
            $fine_id = (int)$_POST['fine_id'];
            $query = "UPDATE fines SET status = 'waived', waived_date = NOW() WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $fine_id);
            $stmt->execute();

            $success_message = "Fine waived successfully.";
        }
    } catch (Exception $e) {
        $error_message = "Error processing request: " . $e->getMessage();
    }
}

// Get financial statistics
$stats = [];

try {
    // Total fines
    $query = "SELECT
                COUNT(*) as total_fines,
                COALESCE(SUM(amount), 0) as total_amount,
                COALESCE(SUM(CASE WHEN status = 'unpaid' THEN amount ELSE 0 END), 0) as unpaid_amount,
                COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
                COALESCE(SUM(CASE WHEN status = 'waived' THEN amount ELSE 0 END), 0) as waived_amount
              FROM fines";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $stats = $stmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total_fines' => 0,
        'total_amount' => 0,
        'unpaid_amount' => 0,
        'paid_amount' => 0,
        'waived_amount' => 0
    ];
}

// Monthly revenue
$monthly_revenue = [];
try {
    $query = "SELECT
                DATE_FORMAT(paid_date, '%Y-%m') as month,
                SUM(amount) as revenue
              FROM fines
              WHERE status = 'paid' AND paid_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
              GROUP BY DATE_FORMAT(paid_date, '%Y-%m')
              ORDER BY month DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $monthly_revenue = $stmt->fetchAll();
} catch (Exception $e) {
    $monthly_revenue = [];
}

// Get recent fines with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$fines = [];
$total_fines = 0;
$total_pages = 1;

try {
    // Check if members table has the right column names
    $member_columns = $db->query("SHOW COLUMNS FROM members LIKE 'name'")->fetch();
    $name_column = $member_columns ? 'name' : 'CONCAT(first_name, " ", last_name)';

    $query = "SELECT f.*, $name_column as member_name, m.email as member_email,
                     b.title as book_title, bl.due_date
              FROM fines f
              JOIN members m ON f.member_id = m.id
              LEFT JOIN book_loans bl ON f.loan_id = bl.id
              LEFT JOIN books b ON bl.book_id = b.id
              ORDER BY f.created_date DESC
              LIMIT :offset, :per_page";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':per_page', $per_page, PDO::PARAM_INT);
    $stmt->execute();
    $fines = $stmt->fetchAll();

    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM fines";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute();
    $total_fines = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_fines / $per_page);
} catch (Exception $e) {
    $error_message = "Error loading fines: " . $e->getMessage();
}

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

function getStatusBadge($status) {
    switch ($status) {
        case 'paid': return 'bg-success';
        case 'unpaid': return 'bg-danger';
        case 'waived': return 'bg-secondary';
        default: return 'bg-warning';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Management - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .fine-row {
            transition: background-color 0.2s ease;
        }
        .fine-row:hover {
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        .sidebar .nav-link {
            color: #333;
        }
        .sidebar .nav-link.active {
            color: #007bff;
            background-color: rgba(0, 123, 255, .1);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-book me-2"></i>LMS Admin
            </a>
            <span class="navbar-text">
                Financial Management
            </span>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="bi bi-house me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="financial_management.php">
                                <i class="bi bi-currency-dollar me-2"></i>Financial Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payment_reports.php">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Payment Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-currency-dollar me-2"></i>Financial Management
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="generateReport()">
                                <i class="bi bi-file-earmark-bar-graph me-1"></i> Generate Report
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportData()">
                                <i class="bi bi-download me-1"></i> Export Data
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i><?php echo h($success_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo h($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Financial Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Fines</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['total_amount'] ?? 0); ?></h3>
                                        <small class="text-white-50"><?php echo $stats['total_fines'] ?? 0; ?> fines</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-currency-dollar fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-danger">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Unpaid Fines</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['unpaid_amount'] ?? 0); ?></h3>
                                        <small class="text-white-50">Outstanding</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Paid Fines</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['paid_amount'] ?? 0); ?></h3>
                                        <small class="text-white-50">Collected</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-check-circle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-secondary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Waived Fines</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['waived_amount'] ?? 0); ?></h3>
                                        <small class="text-white-50">Forgiven</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-x-circle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Revenue Chart -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-bar-chart me-2"></i>Monthly Revenue (Last 12 Months)
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="revenueChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-pie-chart me-2"></i>Fine Status Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="statusChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fines List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>Recent Fines
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($fines)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-currency-dollar fs-1 text-muted"></i>
                                <p class="text-muted mt-3">No fines found</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Member</th>
                                            <th>Book</th>
                                            <th>Amount</th>
                                            <th>Due Date</th>
                                            <th>Fine Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($fines as $fine): ?>
                                            <tr class="fine-row">
                                                <td>
                                                    <strong><?php echo h($fine['member_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo h($fine['member_email']); ?></small>
                                                </td>
                                                <td><?php echo h($fine['book_title'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <strong class="text-danger"><?php echo formatCurrency($fine['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($fine['due_date']): ?>
                                                        <?php echo date('M j, Y', strtotime($fine['due_date'])); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($fine['created_date'])); ?></td>
                                                <td>
                                                    <span class="badge <?php echo getStatusBadge($fine['status']); ?>">
                                                        <?php echo ucfirst($fine['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($fine['status'] === 'unpaid'): ?>
                                                        <div class="btn-group btn-group-sm">
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="fine_id" value="<?php echo $fine['id']; ?>">
                                                                <button type="submit" name="mark_paid" class="btn btn-success btn-sm" title="Mark as Paid">
                                                                    <i class="bi bi-check"></i>
                                                                </button>
                                                            </form>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="fine_id" value="<?php echo $fine['id']; ?>">
                                                                <button type="submit" name="waive_fine" class="btn btn-secondary btn-sm" title="Waive Fine" onclick="return confirm('Are you sure you want to waive this fine?')">
                                                                    <i class="bi bi-x"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Fines pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                            </li>
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_reverse(array_column($monthly_revenue, 'month'))) . "'"; ?>],
                datasets: [{
                    label: 'Revenue',
                    data: [<?php echo implode(',', array_reverse(array_column($monthly_revenue, 'revenue'))); ?>],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                }
            }
        });

        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Paid', 'Unpaid', 'Waived'],
                datasets: [{
                    data: [
                        <?php echo $stats['paid_amount'] ?? 0; ?>,
                        <?php echo $stats['unpaid_amount'] ?? 0; ?>,
                        <?php echo $stats['waived_amount'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function generateReport() {
            alert('Report generation feature coming soon!');
        }

        function exportData() {
            alert('Data export feature coming soon!');
        }
    </script>
</body>
</html>
