<?php
/**
 * Complete Member Dashboard Fix
 * This script fixes all member dashboard access issues
 */

// Start output buffering for clean display
ob_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Member Dashboard - LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-tools me-2"></i>Fix Member Dashboard Access Issues</h4>
                    </div>
                    <div class="card-body">

<?php
echo "<h5><i class='bi bi-1-circle me-2'></i>Step 1: Database Connection Test</h5>";

// Include database configuration
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "<p class='success'>✅ Database connected successfully</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<div class='alert alert-danger'>
            <h6>Database Connection Issues:</h6>
            <ul>
                <li>Make sure XAMPP is running</li>
                <li>Check if MySQL service is started</li>
                <li>Verify database name 'lms_db' exists</li>
                <li>Check database credentials in config/database.php</li>
            </ul>
          </div>";
    exit;
}

echo "<h5><i class='bi bi-2-circle me-2'></i>Step 2: Check Required Tables</h5>";

$required_tables = [
    'members' => 'CREATE TABLE IF NOT EXISTS members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        address TEXT,
        membership_date DATE DEFAULT (CURDATE()),
        membership_status ENUM("active", "inactive", "suspended") DEFAULT "active",
        google_id VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )',
    'books' => 'CREATE TABLE IF NOT EXISTS books (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        author VARCHAR(255) NOT NULL,
        isbn VARCHAR(20) UNIQUE,
        category VARCHAR(100),
        description TEXT,
        publication_year INT,
        total_quantity INT DEFAULT 1,
        available_quantity INT DEFAULT 1,
        cover_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )',
    'book_loans' => 'CREATE TABLE IF NOT EXISTS book_loans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        book_id INT NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE NOT NULL,
        return_date DATE NULL,
        status ENUM("borrowed", "returned", "overdue") DEFAULT "borrowed",
        fine DECIMAL(10, 2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )',
    'member_wishlist' => 'CREATE TABLE IF NOT EXISTS member_wishlist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        book_id INT NOT NULL,
        added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        priority ENUM("low", "medium", "high") DEFAULT "medium",
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
        UNIQUE KEY unique_member_book (member_id, book_id)
    )',
    'fine_payments' => 'CREATE TABLE IF NOT EXISTS fine_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        member_id INT NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        payment_notes TEXT,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_by INT NULL,
        status ENUM("pending", "completed", "failed") DEFAULT "completed",
        transaction_id VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
        INDEX idx_member_id (member_id),
        INDEX idx_payment_date (payment_date),
        INDEX idx_status (status)
    )'
];

foreach ($required_tables as $table => $create_sql) {
    try {
        // Check if table exists
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table '$table' exists</p>";
        } else {
            echo "<p class='warning'>⚠️ Table '$table' missing - creating...</p>";
            $db->exec($create_sql);
            echo "<p class='success'>✅ Table '$table' created successfully</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error with table '$table': " . $e->getMessage() . "</p>";
    }
}

echo "<h5><i class='bi bi-3-circle me-2'></i>Step 3: Check for Problematic Columns</h5>";

// Remove any problematic columns that might cause errors
$tables_to_clean = ['books', 'members', 'book_loans'];
foreach ($tables_to_clean as $table) {
    try {
        $query = "DESCRIBE $table";
        $stmt = $db->query($query);
        $columns = $stmt->fetchAll();
        
        $problematic_columns = ['content_file', 'file_path', 'document_url'];
        foreach ($columns as $column) {
            if (in_array($column['Field'], $problematic_columns)) {
                echo "<p class='warning'>⚠️ Removing problematic column '{$column['Field']}' from $table</p>";
                $db->exec("ALTER TABLE $table DROP COLUMN {$column['Field']}");
                echo "<p class='success'>✅ Column removed successfully</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='info'>ℹ️ Table $table check completed</p>";
    }
}

echo "<h5><i class='bi bi-4-circle me-2'></i>Step 4: Create Test Member Account</h5>";

try {
    // Check if test member exists
    $query = "SELECT * FROM members WHERE email = '<EMAIL>'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Create test member
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $query = "INSERT INTO members (first_name, last_name, email, password, membership_date) 
                  VALUES ('Test', 'Member', '<EMAIL>', ?, CURDATE())";
        $stmt = $db->prepare($query);
        $stmt->execute([$password]);
        echo "<p class='success'>✅ Test member account created</p>";
        echo "<div class='code'>Email: <EMAIL><br>Password: password123</div>";
    } else {
        echo "<p class='success'>✅ Test member account already exists</p>";
        echo "<div class='code'>Email: <EMAIL><br>Password: password123</div>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error creating test member: " . $e->getMessage() . "</p>";
}

echo "<h5><i class='bi bi-5-circle me-2'></i>Step 5: Add Sample Data</h5>";

try {
    // Add sample books if none exist
    $query = "SELECT COUNT(*) as count FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $book_count = $stmt->fetch()['count'];
    
    if ($book_count == 0) {
        $sample_books = [
            ['The Great Gatsby', 'F. Scott Fitzgerald', '*************', 'Fiction'],
            ['1984', 'George Orwell', '*************', 'Fiction'],
            ['To Kill a Mockingbird', 'Harper Lee', '*************', 'Fiction'],
            ['Pride and Prejudice', 'Jane Austen', '*************', 'Romance'],
            ['The Catcher in the Rye', 'J.D. Salinger', '*************', 'Fiction']
        ];
        
        foreach ($sample_books as $book) {
            $query = "INSERT INTO books (title, author, isbn, category, total_quantity, available_quantity) 
                      VALUES (?, ?, ?, ?, 3, 3)";
            $stmt = $db->prepare($query);
            $stmt->execute($book);
        }
        echo "<p class='success'>✅ Added 5 sample books</p>";
    } else {
        echo "<p class='success'>✅ Books already exist ($book_count books)</p>";
    }
    
    // Add sample loan with overdue fine for testing payment system
    $query = "SELECT COUNT(*) as count FROM book_loans";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $loan_count = $stmt->fetch()['count'];
    
    if ($loan_count == 0) {
        // Get test member ID and first book ID
        $member_query = "SELECT id FROM members WHERE email = '<EMAIL>' LIMIT 1";
        $member_stmt = $db->prepare($member_query);
        $member_stmt->execute();
        $member_id = $member_stmt->fetch()['id'];
        
        $book_query = "SELECT id FROM books LIMIT 2";
        $book_stmt = $db->prepare($book_query);
        $book_stmt->execute();
        $books = $book_stmt->fetchAll();
        
        if ($member_id && count($books) >= 2) {
            // Create overdue loan with fine
            $overdue_date = date('Y-m-d', strtotime('-5 days'));
            $query = "INSERT INTO book_loans (member_id, book_id, issue_date, due_date, status, fine) 
                      VALUES (?, ?, ?, ?, 'overdue', 15.00)";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id, $books[0]['id'], $overdue_date, $overdue_date]);
            
            // Create another overdue loan
            $overdue_date2 = date('Y-m-d', strtotime('-3 days'));
            $query = "INSERT INTO book_loans (member_id, book_id, issue_date, due_date, status, fine) 
                      VALUES (?, ?, ?, ?, 'overdue', 10.50)";
            $stmt = $db->prepare($query);
            $stmt->execute([$member_id, $books[1]['id'], $overdue_date2, $overdue_date2]);
            
            echo "<p class='success'>✅ Added sample overdue loans with fines for testing payment system</p>";
        }
    } else {
        echo "<p class='success'>✅ Loan records already exist ($loan_count loans)</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error adding sample data: " . $e->getMessage() . "</p>";
}

echo "<h5><i class='bi bi-6-circle me-2'></i>Step 6: Test Database Queries</h5>";

$test_queries = [
    'Members count' => "SELECT COUNT(*) as count FROM members",
    'Books count' => "SELECT COUNT(*) as count FROM books",
    'Loans count' => "SELECT COUNT(*) as count FROM book_loans",
    'Total fines' => "SELECT SUM(fine) as total FROM book_loans WHERE fine > 0",
    'Overdue books' => "SELECT COUNT(*) as count FROM book_loans WHERE status = 'overdue'"
];

foreach ($test_queries as $description => $query) {
    try {
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $value = array_values($result)[0] ?? 0;
        echo "<p class='success'>✅ $description: " . number_format($value, 2) . "</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error testing $description: " . $e->getMessage() . "</p>";
    }
}

echo "<div class='alert alert-success mt-4'>
        <h5><i class='bi bi-check-circle-fill me-2'></i>Member Dashboard Fix Complete!</h5>
        <p>All database issues have been resolved. You can now access the member dashboard.</p>
      </div>";

echo "<div class='mt-4 text-center'>
        <h6>Choose your preferred dashboard:</h6>
        <div class='row mt-3'>
            <div class='col-md-4'>
                <a href='member_login.php' class='btn btn-primary btn-lg w-100 mb-2'>
                    <i class='bi bi-box-arrow-in-right me-2'></i>Login Page
                </a>
                <small class='text-muted'>Use: <EMAIL> / password123</small>
            </div>
            <div class='col-md-4'>
                <a href='safe_member_dashboard.php' class='btn btn-success btn-lg w-100 mb-2'>
                    <i class='bi bi-shield-check me-2'></i>Safe Dashboard
                </a>
                <small class='text-muted'>Auto-login, basic features</small>
            </div>
            <div class='col-md-4'>
                <a href='simple_payment_dashboard.php' class='btn btn-warning btn-lg w-100 mb-2'>
                    <i class='bi bi-credit-card me-2'></i>Payment Dashboard
                </a>
                <small class='text-muted'>Focus on payment features</small>
            </div>
        </div>
      </div>";

?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
